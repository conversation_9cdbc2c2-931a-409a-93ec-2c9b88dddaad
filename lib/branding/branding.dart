import 'package:flutter/material.dart';

const String companyName =
    String.fromEnvironment('company', defaultValue: 'nds');

// Default branding values - can be customized per company if needed
const String splashScreenLoadingPageCircularWhite =
    "assets/images/loading_circular_white.png";
const String splashScreenLoadingPageCompanyLogoBg =
    "assets/images/company_logo_bg.png";
const String splashScreenLoadingPageCompanyLogo1 =
    "assets/images/company_logo_1.png";
const String splashScreenLoadingScreenCompanyLogo2 =
    "assets/images/company_logo_2.png";
const String splashScreenLoadingScreenCompanyLogo3 =
    "assets/images/company_logo_3.png";
const String loginScreenLogo1 = "assets/images/login_logo.png";
const String afterConnectionCompanyLabel = "Connected to Vehicle";
const String clusterTitleRowCompanyLogo = "assets/images/cluster_logo.png";
const Color loginThemeColor = Color(0xFF2196F3);
const String contactPhoneNumber = "******-123-4567";
const String contactMail = "<EMAIL>";
const String otpSenderId = "COMPANY";
const String website = "https://company.com";
const String iosAppId = "*********";
const String androidPackageName = "com.company.app";
const int noOfWheels = 2;
