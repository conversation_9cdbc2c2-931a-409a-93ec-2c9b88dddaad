import 'package:flutter/material.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/core/features/feature_service.dart';
import 'package:nds_app/company/baseScreens/insights/base_insights.dart';
import 'package:nds_app/company/templates/insights/insights_prodred_screen.dart';
import 'package:nds_app/company/templates/insights/insights_standard_screen.dart';

/// Factory class to create company-specific insights screens based on the current company configuration
class InsightsFactory {
  /// Creates and returns the appropriate insights widget based on the company name
  static Widget createInsights(Color color, ColorType colorType) {
    // Use company-specific validation to determine template
    return _getInsightsTemplate(color: color, colorType: colorType);
  }

  /// Returns the appropriate insights template based on company validation requirements
  static BaseInsights _getInsightsTemplate({
    required Color color,
    required ColorType colorType,
  }) {
    // Template selection based on feature configuration:
    // ProdRed: simplified view - statistics only
    // Others: full toggle functionality
    if (FeatureService.shouldUseEnterpriseLayout()) {
      return InsightsProdRedScreen(color: color, colorType: colorType);
    }
    return InsightsStandardScreen(color: color, colorType: colorType);
  }

  /// Returns the insights template class name for debugging purposes
  static String getInsightsClassName() {
    if (FeatureService.shouldUseEnterpriseLayout()) {
      return 'InsightsProdRedScreen';
    }
    return 'InsightsStandardScreen';
  }

  /// Returns the template type being used
  static String getTemplateType() {
    if (FeatureService.shouldUseEnterpriseLayout()) {
      return 'ProdRed Insights (Statistics Only)';
    }
    return 'Standard Insights (Full Toggle Functionality)';
  }
}
