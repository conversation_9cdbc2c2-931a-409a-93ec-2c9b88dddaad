import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_bloc.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_event.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/models/enums/vehicle_performace_type.dart';
import 'package:nds_app/models/range_performance_model.dart';
import 'package:nds_app/widgets/vehicle/reusableWidgets/reusable_leadership_date_filter.dart';
import 'package:nds_app/widgets/vehicle/reusableWidgets/reusable_leadership_insight_header.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

class LeadershipRangeView extends StatelessWidget {
  final int entityId;
  final VehicleType vehicleType;

  const LeadershipRangeView({
    super.key,
    required this.entityId,
    required this.vehicleType,
  });

  String _getDateRangeText(
      String selectedTimeFilter, DateTime startDate, DateTime endDate) {
    final dayFmt = DateFormat('MMM dd');

    if (selectedTimeFilter == homeScreenText["text32"]! ||
        selectedTimeFilter == homeScreenText["text33"]!) {
      return dayFmt.format(startDate);
    }
    if (selectedTimeFilter == homeScreenText["text34"]! ||
        selectedTimeFilter == homeScreenText["text36"]!) {
      return '${dayFmt.format(startDate)} - ${dayFmt.format(endDate)}';
    }
    // Month
    return DateFormat('MMM yyyy').format(startDate);
  }

  bool _shouldShowTableToggle() {
    // Show table toggle only for engineer role as per wireframe requirements
    // TODO: Get from user context/API
    return true; // Assuming engineer role for now
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return BlocBuilder<LeadershipPerformanceBloc, LeadershipPerformanceState>(
      builder: (context, state) {
        return Padding(
          padding:
              EdgeInsets.symmetric(horizontal: 20 / 375 * dimensions.width),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              reusableLeadershipInsightHeader(
                dimensions: dimensions,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      leadershipText['overall_range']!,
                      style: poppinsTextStyle(
                        20,
                        Colors.white,
                        FontWeight.w600,
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.ideographic,
                      children: [
                        Text(
                          state.hasRangeData
                              ? state.rangePerformance!.overallRange
                                  .toStringAsFixed(0)
                              : '0',
                          style: poppinsTextStyle(
                            64,
                            Colors.white,
                            FontWeight.w500,
                          ),
                        ),
                        Text(
                          leadershipText['km_unit']!,
                          style: poppinsTextStyle(
                            20,
                            Colors.white,
                            FontWeight.w500,
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
              SizedBox(height: 24 / 896 * dimensions.height),
              ReusableDateFilter(
                dimensions: dimensions,
                selectedValue: state.selectedTimeFilter,
                options: [
                  homeScreenText["text32"]!, // Today
                  homeScreenText["text33"]!, // Yesterday
                  homeScreenText["text34"]!, // This Week
                  homeScreenText["text36"]!, // Last Week
                  homeScreenText["text35"]!, // This Month
                  homeScreenText["text37"]!, // Last Month
                ],
                onChanged: (value) {
                  context.read<LeadershipPerformanceBloc>().add(
                        ChangeTimeFilterEvent(
                          timeFilter: value,
                          entityId: entityId,
                          vehicleType: vehicleType,
                          summaryType: SummaryType.range,
                        ),
                      );
                },
              ),

              SizedBox(height: 24 / 896 * dimensions.height),

              // Show loading, error, or content
              if (state.isLoading)
                Center(
                  child: Image.asset(
                    isTwoWheels
                        ? loaderGifImages['2Wheels']!
                        : loaderGifImages['3Wheels']!,
                  ),
                )
              else if (state.hasError)
                Center(
                  child: Column(
                    children: [
                      Text('Error: ${state.message}'),
                      ElevatedButton(
                        onPressed: () {
                          context.read<LeadershipPerformanceBloc>().add(
                                RefreshPerformanceDataEvent(
                                  entityId: entityId,
                                  vehicleType: vehicleType,
                                  summaryType: SummaryType.range,
                                ),
                              );
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              else ...[
                // Average Range Section
                _buildAverageRangeSection(dimensions, state),
                SizedBox(height: 24 / 896 * dimensions.height),

                // Overall Performance Section
                _buildOverallPerformanceSection(context, dimensions, state),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildAverageRangeSection(
      Dimensions dimensions, LeadershipPerformanceState state) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20 / 375 * dimensions.width),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorGrey300),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                leadershipText['avg_range']!,
                style: poppinsTextStyle(
                  16 / 414 * dimensions.width,
                  Colors.black,
                  FontWeight.w500,
                ),
              ),
              SizedBox(height: 4 / 896 * dimensions.height),
              Text(
                _getDateRangeText(
                    state.selectedTimeFilter, state.startTime, state.endTime),
                style: poppinsTextStyle(
                  14 / 414 * dimensions.width,
                  Colors.grey[600]!,
                  FontWeight.w400,
                ),
              ),
            ],
          ),
          Text(
            state.hasRangeData
                ? '${state.rangePerformance!.avgRange.toStringAsFixed(0)} Km'
                : '0 Km',
            style: poppinsTextStyle(
              24 / 414 * dimensions.width,
              Colors.black,
              FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // ViewLevel toggle removed; parent controls level

  Widget _buildOverallPerformanceSection(BuildContext context,
      Dimensions dimensions, LeadershipPerformanceState state) {
    // Use BLoC state for fleet level and graph view toggle
    bool isFleetLevel = vehicleType == VehicleType.fleet;
    bool isGraphView = state.isGraphView;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20 / 375 * dimensions.width),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorGrey300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child:                 Text(
                  leadershipText['overall_performance']!,
                  style: poppinsTextStyle(
                    16 / 414 * dimensions.width,
                    Colors.black,
                    FontWeight.w500,
                  ),
                ),
              ),
              if (isFleetLevel && _shouldShowTableToggle())
                _buildViewToggle(context, dimensions, isGraphView),
            ],
          ),
          SizedBox(height: 20 / 896 * dimensions.height),
          // Show graph or table view based on BLoC state
          isGraphView
              ? _buildGraphView(dimensions, state)
              : _buildTableView(dimensions, state),
        ],
      ),
    );
  }

  Widget _buildViewToggle(
      BuildContext context, Dimensions dimensions, bool isGraphView) {
    double pillWidth = 46 / 414 * dimensions.width;
    double pillHeight = 28 / 896 * dimensions.height;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          leadershipText['graph_view']!,
          style: poppinsTextStyle(
              14 / 414 * dimensions.width, colorGrey600, FontWeight.w400),
        ),
        SizedBox(width: 8 / 414 * dimensions.width),
        GestureDetector(
          onTap: () {
            // Toggle view using BLoC
            context
                .read<LeadershipPerformanceBloc>()
                .add(const ToggleViewEvent());
          },
          child: Container(
            width: pillWidth,
            height: pillHeight,
            padding: EdgeInsets.all(4 / 414 * dimensions.width),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFF0C4364), width: 2),
              borderRadius: BorderRadius.circular(20),
              color: Colors.transparent,
            ),
            child: AnimatedAlign(
              alignment:
                  isGraphView ? Alignment.centerLeft : Alignment.centerRight,
              duration: const Duration(milliseconds: 200),
              child: Container(
                width: pillHeight - 8 / 896 * dimensions.height,
                height: pillHeight - 8 / 896 * dimensions.height,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xFF0C4364),
                ),
              ),
            ),
          ),
        ),
        SizedBox(width: 8 / 414 * dimensions.width),
        Text(
          leadershipText['table_view']!,
          style: poppinsTextStyle(
              14 / 414 * dimensions.width, colorGrey600, FontWeight.w400),
        ),
      ],
    );
  }

  Widget _buildGraphView(
      Dimensions dimensions, LeadershipPerformanceState state) {
    // Convert range performance graph data to chart format
    List<Map<String, dynamic>> chartData = [];

    // Determine which graph data to use based on time filter
    List<GraphPoint> graphPoints = _getGraphDataForTimeFilter(state);

    if (state.hasRangeData && graphPoints.isNotEmpty) {
      chartData = _convertGraphPointsToChartData(graphPoints, state);
    } else {
      // No data available - show empty chart
      chartData = [];
    }

    if (chartData.isEmpty) {
      return SizedBox(
        height: 200 / 896 * dimensions.height,
        child: Center(
          child: Text(
            leadershipText['no_range_data']!,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return SizedBox(
      height: 200 / 896 * dimensions.height,
      child: SfCartesianChart(
        primaryXAxis: _buildXAxis(state.selectedTimeFilter),
        primaryYAxis: NumericAxis(
          labelFormat: '{value} km',
          majorGridLines: const MajorGridLines(
            width: 1,
            dashArray: <double>[4, 4],
            color: colorGrey300,
          ),
          axisLine: const AxisLine(width: 0),
        ),
        plotAreaBorderWidth: 0,
        series: <AreaSeries>[
          AreaSeries<Map<String, dynamic>, DateTime>(
            dataSource: chartData,
            xValueMapper: (data, _) => data['date'] as DateTime,
            yValueMapper: (data, _) => data['value'] as double,
            name: 'Average Range',
            gradient: const LinearGradient(
              colors: [
                Color(0x801E3A8A), // Semi-transparent blue at bottom
                Color(0xFF3B82F6), // Solid blue at top
              ],
              stops: [0.0, 1.0],
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
            ),
            borderColor: const Color(0xFF1E3A8A),
            borderWidth: 2,
            // Add data point markers for better visibility
            markerSettings: const MarkerSettings(
              isVisible: true,
              height: 4,
              width: 4,
              color: Color(0xFF1E3A8A),
              borderColor: Colors.white,
              borderWidth: 1,
            ),
            // Enable data labels for key points
            dataLabelSettings: const DataLabelSettings(
              isVisible:
                  false, // Keep false to avoid clutter, can be enabled if needed
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableView(
      Dimensions dimensions, LeadershipPerformanceState state) {
    List<Map<String, dynamic>> vehicles = [];

    if (state.hasRangeData && state.rangePerformance!.topVehicles != null) {
      vehicles = state.rangePerformance!.topVehicles!.map((vehicle) {
        return {
          'primaryText': vehicle.vehImei ?? vehicle.vehId ?? 'Unknown',
          'secondaryText': vehicle.modelName ?? 'Unknown Model',
          'range': vehicle.avgRange,
        };
      }).toList();
    } else {
      // No data available - show empty list
      vehicles = [];
    }

    if (vehicles.isEmpty) {
      return Center(
        child: Text(
          leadershipText['no_vehicle_data_available']!,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return Column(
      children: vehicles
          .map((vehicle) => _buildVehicleRow(vehicle, dimensions))
          .toList(),
    );
  }

  Widget _buildVehicleRow(Map<String, dynamic> vehicle, Dimensions dimensions) {
    return Container(
      margin: EdgeInsets.only(bottom: 12 / 896 * dimensions.height),
      padding: EdgeInsets.all(16 / 375 * dimensions.width),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colorGrey300),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                vehicle['primaryText'],
                style: poppinsTextStyle(
                  16 / 414 * dimensions.width,
                  Colors.black,
                  FontWeight.w500,
                ),
              ),
              SizedBox(height: 4 / 896 * dimensions.height),
              Text(
                vehicle['secondaryText'],
                style: poppinsTextStyle(
                  12 / 414 * dimensions.width,
                  Colors.grey[600]!,
                  FontWeight.w400,
                ),
              ),
            ],
          ),
          Text(
            '${vehicle['range'].toStringAsFixed(0)} Km',
            style: poppinsTextStyle(
              18 / 414 * dimensions.width,
              Colors.black,
              FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Convert GraphPoints to chart data with proper DateTime conversion
  List<Map<String, dynamic>> _convertGraphPointsToChartData(
      List<GraphPoint> graphPoints, LeadershipPerformanceState state) {
    final timeFilter = state.selectedTimeFilter;
    final startTime = state.startTime;
    final endTime = state.endTime;

    return graphPoints.where((point) {
      // Filter out data points that don't belong to current period
      if (timeFilter == 'Today' || timeFilter == 'Yesterday') {
        // For day view: only show hours 0-23
        return point.x >= 0 && point.x <= 23;
      } else if (timeFilter == 'This Week' || timeFilter == 'Last Week') {
        // For week view: only show days that belong to the current week/month
        final currentMonth = startTime.month;
        final currentYear = startTime.year;
        try {
          final testDate = DateTime(currentYear, currentMonth, point.x);
          // Check if this day exists in current month and is within the time range
          return testDate.month == currentMonth &&
              testDate.millisecondsSinceEpoch >=
                  startTime.millisecondsSinceEpoch &&
              testDate.millisecondsSinceEpoch <= endTime.millisecondsSinceEpoch;
        } catch (e) {
          // Invalid date (like day 31 in a 30-day month)
          return false;
        }
      } else {
        // For month view: only show days that belong to the current month
        final currentMonth = startTime.month;
        final currentYear = startTime.year;
        try {
          final testDate = DateTime(currentYear, currentMonth, point.x);
          // Check if this day exists in current month
          return testDate.month == currentMonth;
        } catch (e) {
          // Invalid date (like day 31 in a 30-day month)
          return false;
        }
      }
    }).map((point) {
      DateTime dateTime;

      if (timeFilter == 'Today' || timeFilter == 'Yesterday') {
        // For day view: x is hour number (0-23)
        final targetDate = timeFilter == 'Today'
            ? DateTime.now()
            : DateTime.now().subtract(const Duration(days: 1));
        dateTime = DateTime(
          targetDate.year,
          targetDate.month,
          targetDate.day,
          point.x, // hour
          0, // minute
          0, // second
        );
      } else if (timeFilter == 'This Week' || timeFilter == 'Last Week') {
        // For week view: x is day number of the month
        dateTime = DateTime(
          startTime.year,
          startTime.month,
          point.x, // day of month from API
        );
      } else {
        // For month view: x is day number (1-31) - only current month
        dateTime = DateTime(
          startTime.year,
          startTime.month,
          point.x, // day of month
        );
      }

      return {
        'date': dateTime,
        'value': point.y,
      };
    }).toList();
  }

  /// Build X-axis configuration based on time filter
  DateTimeAxis _buildXAxis(String timeFilter) {
    if (timeFilter == 'Today' || timeFilter == 'Yesterday') {
      // For day view: show hours (0-23)
      return DateTimeAxis(
        isVisible: true,
        dateFormat: DateFormat('HH:mm'), // Show hours like 00:00, 04:00, etc.
        majorGridLines: const MajorGridLines(width: 0),
        axisLine: const AxisLine(width: 0),
        intervalType: DateTimeIntervalType.hours,
        interval: 4, // Show every 4 hours to avoid crowding
        labelRotation: 0,
      );
    } else if (timeFilter == 'This Week' || timeFilter == 'Last Week') {
      // For week view: show day names (Mon, Tue, Wed, etc.)
      return DateTimeAxis(
        isVisible: true,
        dateFormat: DateFormat('EEE'), // Show day names like Mon, Tue, Wed
        majorGridLines: const MajorGridLines(width: 0),
        axisLine: const AxisLine(width: 0),
        intervalType: DateTimeIntervalType.days,
        interval: 1, // Show every day
        labelRotation: 0,
      );
    } else {
      // For month view: show day numbers (1-31)
      return DateTimeAxis(
        isVisible: true,
        dateFormat:
            DateFormat('d'), // Show day numbers like 31, 1, 5, 10, 15, etc.
        majorGridLines: const MajorGridLines(width: 0),
        axisLine: const AxisLine(width: 0),
        intervalType: DateTimeIntervalType.days,
        interval: 3, // Show every 3 days to show more labels including 31
        labelRotation: 0,
      );
    }
  }

  /// Get the appropriate graph data based on the selected time filter
  List<GraphPoint> _getGraphDataForTimeFilter(
      LeadershipPerformanceState state) {
    if (!state.hasRangeData) return [];

    final graphData = state.rangePerformance!.graph;
    final timeFilter = state.selectedTimeFilter;

    // Determine which data to use based on time filter
    if (timeFilter == 'Today' || timeFilter == 'Yesterday') {
      // For day filters, use day data (all trips in that day)
      return graphData.day;
    } else if (timeFilter == 'This Week' || timeFilter == 'Last Week') {
      // For week filters, use week data (7 days)
      return graphData.week;
    } else if (timeFilter == 'This Month' || timeFilter == 'Last Month') {
      // For month filters, use month data (30/31 days)
      return graphData.month;
    }

    // Default to day data
    return graphData.day;
  }

  // Bottom sheet date picker removed; dropdown used instead.
}
