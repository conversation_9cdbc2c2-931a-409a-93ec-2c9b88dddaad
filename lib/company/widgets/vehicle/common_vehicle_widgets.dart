import 'package:flutter/material.dart';
import 'package:nds_app/core/features/feature_service.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/widgets/common/custom_button.dart';

class CommonVehicleWidgets {
  /// Gets the vehicle health button based on company configuration
  /// For ProdRed users, the button is hidden, for others it's visible
  static Widget getVehicleHealthButton(
    BuildContext context,
    Color color,
    void Function() goVehicleHealth,
  ) {
    return FeatureService.vehicleHealthButtonWidget(
      Padding(
        padding: const EdgeInsets.fromLTRB(32, 8, 32, 8),
        child: SizedBox(
          width: double.infinity,
          height: 56,
          child: CustomButton.elevated(
            onPressed: () async {
              goVehicleHealth();
            },
            backgroundColor: color,
            borderRadius: 45.0,
            elevation: 4,
            text: vehicleScreen["vehicleHealth"]!,
            foregroundColor: colorWhite,
          ),
        ),
      ),
    );
  }

  /// Determines the organization type for API calls based on company
  /// B2C/Lapa users get B2C organization type, others get B2B
  static String getOrganizationType() {
    return FeatureService.getApiOrganizationType();
  }

  /// Determines if B2C/Lapa layout should be used
  /// Returns true for B2C and Lapa users
  static bool shouldUseB2CLayout() {
    return FeatureService.shouldUseConsumerLayout();
  }

  /// Checks if the current user is a ProdRed user
  static bool isProdRedCompany() {
    return FeatureService.shouldUseEnterpriseLayout();
  }

  /// Checks if the current company is B2C or Lapa
  static bool isB2COrLapaCompany() {
    return FeatureService.shouldUseConsumerLayout();
  }

  /// Gets the appropriate padding for vehicle health screens
  /// B2C/Lapa users get full padding, others get no padding
  static EdgeInsets getVehicleHealthPadding() {
    return FeatureService.getVehicleHealthPadding();
  }
}
