import 'package:flutter/material.dart';
import 'package:nds_app/company/baseScreens/profile_screens/base_profile_screens.dart';
import 'package:nds_app/company/templates/profile_screens/setting_standard_screen.dart';

/// Factory class to create company-specific setting screens based on the current company configuration
class SettingFactory {
  /// Creates and returns the appropriate setting widget based on the company name
  static Widget createSetting({
    required void Function() onBackPressed,
  }) {
    // All company currently use the same template
    // If company-specific validation is needed in the future, add switch statement here
    return _getSettingTemplate(onBackPressed: onBackPressed);
  }

  /// Returns the appropriate setting template
  static BaseSettingScreen _getSettingTemplate({
    required void Function() onBackPressed,
  }) {
    // All companies use standard template
    return SettingStandardScreen(onBackPressed: onBackPressed);
  }

  /// Returns the setting class name for debugging purposes
  static String getSettingClassName() {
    return 'SettingStandardScreen';
  }
}
