import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/core/features/feature_service.dart';
import 'package:nds_app/common/prodred_usertype.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/services/push_notification_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoginService {
  static const int _timeoutInSecond = 10;

  static Future<String?> checkLoginStatus() async {
    final prefs = await SharedPreferences.getInstance();
    String? authCode = prefs.getString(authTokenKey);
    if (authCode != null) {
      try {
        Map<String, dynamic> token = parseJwt(authCode);

        if (DateTime.fromMillisecondsSinceEpoch(token["exp"] * 1000)
            .isBefore(DateTime.now())) {
          authCode = null;
          await prefs.remove(authTokenKey);
        } else {
          // Set isProdRedLeadership from JWT token when token is valid
          await _setIsProdRedLeadershipFromToken(token, prefs);
        }
      } catch (e) {
        debugPrint(e.toString());
      }
    }

    return authCode;
  }

  static Future<String?> sendLoginOtp(String phoneNumber) async {
    String? errorMsg;
    http.Response response = await _initiateGetCall(ApiUrls.otpPath.getUrl(),
        params: {"phoneNumber": phoneNumber});
    if (response.statusCode != 200) {
      JsonDecoder decoder = const JsonDecoder();
      Map<String, dynamic> sendOtpResponse = decoder.convert(response.body);
      errorMsg = sendOtpResponse["error"];
      errorMsg ??= sendOtpResponse['message'];
    }
    return errorMsg;
  }

  static Future<String?> verifyLoginOtp(String otp, String phoneNumber) async {
    String? errorStatusCode;
    http.Response response =
        await _initiateGetCall(ApiUrls.verifyOtpPath.getUrl(), params: {
      "otp": otp,
      "phoneNumber": phoneNumber,
    });

    JsonDecoder decoder = const JsonDecoder();
    if (response.statusCode != 200) {
      errorStatusCode = response.statusCode.toString();
    } else {
      Map<String, dynamic> verifyOtpResponse = decoder.convert(response.body);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(authTokenKey, verifyOtpResponse["authToken"]);

      // Parse roles and organizations to determine isProdRedLeadership
      await _setIsProdRedLeadership(verifyOtpResponse, prefs);

      PushNotificationService pushNotificationService =
          PushNotificationService();
      await pushNotificationService.initialize();
    }
    return errorStatusCode;
  }

  /// Determines and stores the isProdRedLeadership value based on user roles and organization
  static Future<void> _setIsProdRedLeadership(
      Map<String, dynamic> verifyOtpResponse, SharedPreferences prefs) async {
    try {
      // Get roles and organizations from response
      List<dynamic>? roles = verifyOtpResponse["roles"];
      List<dynamic>? organisations = verifyOtpResponse["organisations"];

      // Use the determineProdRedLeadership function from prodred_usertype.dart
      bool isProdRedLeadership = await determineProdRedLeadership(
        roles: roles,
        organisations: organisations,
      );

      await prefs.setBool(isProdRedLeadershipKey, isProdRedLeadership);
      debugPrint("isProdRedLeadership set to: $isProdRedLeadership");
    } catch (e) {
      debugPrint("Error determining isProdRedLeadership: $e");
    }
  }

  /// Determines and stores the isProdRedLeadership value from JWT token
  static Future<void> _setIsProdRedLeadershipFromToken(
      Map<String, dynamic> token, SharedPreferences prefs) async {
    try {
      // Get roles and organizations from JWT token
      List<dynamic>? roles = token["roles"];
      List<dynamic>? organisations = token["organisations"];

      // Use the determineProdRedLeadership function from prodred_usertype.dart
      bool isProdRedLeadership = await determineProdRedLeadership(
        roles: roles,
        organisations: organisations,
      );

      await prefs.setBool(isProdRedLeadershipKey, isProdRedLeadership);
      debugPrint("isProdRedLeadership set from token to: $isProdRedLeadership");
    } catch (e) {
      debugPrint("Error determining isProdRedLeadership from token: $e");
    }
  }

  static Future<http.Response> _initiateGetCall(String url,
      {Map? params}) async {
    Map<String, String> getRequestHeaders = {
      "Content-Type": "application/json",
      "manufacturerId": organisationId.toString(),
      "appType": FeatureService.getApiAppType(),
    };
    String paramString = "";
    if (params != null && params.keys.isNotEmpty) {
      for (int i = 0; i < params.keys.length; i++) {
        paramString += params.keys.elementAt(i) +
            "=" +
            params[params.keys.elementAt(i)].toString();
        if (i + 1 < params.keys.length) {
          paramString += "&";
        }
      }
    }
    String urlToCall = url + (paramString.isNotEmpty ? "?" : "") + paramString;
    //debugPrint("===== url : $urlToCall");
    http.Response response = await http
        .get(Uri.parse(urlToCall), headers: getRequestHeaders)
        .timeout(const Duration(seconds: _timeoutInSecond));
    log("===== url : $urlToCall \n ===== status : ${response.statusCode} \n ===== response : ${response.body}");
    // debugPrint("===== response : ${response.body}");

    return response;
  }
}

Map<String, dynamic> parseJwt(String token) {
  final parts = token.split('.');
  if (parts.length != 3) {
    throw Exception('invalid token');
  }

  final payload = _decodeBase64(parts[1]);
  final payloadMap = json.decode(payload);
  if (payloadMap is! Map<String, dynamic>) {
    throw Exception('invalid payload');
  }

  return payloadMap;
}

String _decodeBase64(String str) {
  String output = str.replaceAll('-', '+').replaceAll('_', '/');

  switch (output.length % 4) {
    case 0:
      break;
    case 2:
      output += '==';
      break;
    case 3:
      output += '=';
      break;
    default:
      throw Exception('Illegal base64url string!"');
  }

  return utf8.decode(base64Url.decode(output));
}
