import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_bloc.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_event.dart';
import 'package:nds_app/blocs/vehicle/performance/leadership_performance_state.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/models/battery_performance_model.dart';
import 'package:nds_app/models/enums/vehicle_performace_type.dart';
import 'package:nds_app/utils/alert_dialog_utils.dart';
import 'package:nds_app/widgets/vehicle/reusableWidgets/header_trailer_text_row.dart';
import 'package:nds_app/widgets/vehicle/reusableWidgets/reusable_leadership_date_filter.dart';
import 'package:nds_app/widgets/vehicle/reusableWidgets/reusable_leadership_insight_header.dart';
import 'package:nds_app/widgets/vehicle/reusableWidgets/reusable_summary_card.dart';

import '../../common/image_urls.dart';
import '../../common/strings.dart';
import '../../common/text_styles.dart';

class LeadershipBatteryView extends StatelessWidget {
  final int entityId;
  final VehicleType vehicleType;

  const LeadershipBatteryView({
    super.key,
    required this.entityId,
    required this.vehicleType,
  });

  void _onTimeFilterChanged(BuildContext context, String newValue) {
    context.read<LeadershipPerformanceBloc>().add(
          ChangeTimeFilterEvent(
            timeFilter: newValue,
            entityId: entityId,
            vehicleType: vehicleType,
            summaryType: SummaryType.battery,
          ),
        );
  }

  void _showAlertDialog(BuildContext context, List<String> imeiList) {
    AlertDialogUtils.showVehicleDataOrSnackbar(context, imeiList);
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);

    return BlocBuilder<LeadershipPerformanceBloc, LeadershipPerformanceState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              _buildSummaryHeader(context, dimensions, state),
              const SizedBox(height: 16),
              ReusableDateFilter(
                dimensions: dimensions,
                selectedValue: state.selectedTimeFilter,
                options: [
                  homeScreenText["text32"]!, // Today
                  homeScreenText["text33"]!, // Yesterday
                  homeScreenText["text34"]!, // This Week
                  homeScreenText["text36"]!, // Last Week
                  homeScreenText["text35"]!, // This Month
                  homeScreenText["text37"]!, // Last Month
                ],
                onChanged: (value) {
                  _onTimeFilterChanged(context, value);
                },
              ),
              const SizedBox(height: 16),
              if (state.isLoading)
                Center(
                  child: Image.asset(
                    isTwoWheels
                        ? loaderGifImages['2Wheels']!
                        : loaderGifImages['3Wheels']!,
                  ),
                )
              else if (state.hasError)
                Center(
                  child: Column(
                    children: [
                      Text('Error: ${state.message}'),
                      ElevatedButton(
                        onPressed: () {
                          context.read<LeadershipPerformanceBloc>().add(
                                RefreshPerformanceDataEvent(
                                  entityId: entityId,
                                  vehicleType: vehicleType,
                                  summaryType: SummaryType.battery,
                                ),
                              );
                        },
                        child: Text(leadershipText['retry_button']!),
                      ),
                    ],
                  ),
                )
              else
                ..._buildPerformanceCards(context, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryHeader(BuildContext context, Dimensions dimensions,
      LeadershipPerformanceState state) {
    final batteryData = state.batteryPerformance;
    final isSingleVehicle = vehicleType == VehicleType.vehicle;

    return reusableLeadershipInsightHeader(
      dimensions: dimensions,
      child: Column(
        crossAxisAlignment: isSingleVehicle
            ? CrossAxisAlignment.center
            : CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (!isSingleVehicle)
            Text(
              leadershipText['overall_battery_health']!,
              style: poppinsTextStyle(
                20,
                Colors.white,
                FontWeight.w600,
              ),
            ),
          if (batteryData?.health != null)
            isSingleVehicle
                ? _buildSingleVehicleHealthView(batteryData!)
                : _buildMultiVehicleHealthView(context, batteryData!)
          else
            Text(
              leadershipText['no_health_data']!,
              style: const TextStyle(color: Colors.white),
            ),
        ],
      ),
    );
  }

  Widget _buildSingleVehicleHealthView(BatteryPerformanceModel batteryData) {
    // For single vehicle, show battery health status (Excellent, Good, etc.) and avg time to full charge
    String healthStatus = 'Unknown';

    // Find the health category with count > 0
    for (final entry in batteryData.health!.entries) {
      if (entry.value.count > 0) {
        healthStatus = entry.key;
        break;
      }
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        HeaderTrailerTextRow(
          header: leadershipText['battery_health']!,
          trailer: healthStatus,
          textColor: Colors.white,
          fontSize: 14,
        ),
        const SizedBox(height: 8),
        if (batteryData.avgTimeForFullCharge != null)
          HeaderTrailerTextRow(
            header: leadershipText['avg_time_full_charge']!,
            trailer: batteryData.avgTimeForFullCharge!,
            textColor: Colors.white,
            fontSize: 14,
          ),
      ],
    );
  }

  Widget _buildMultiVehicleHealthView(
      BuildContext context, BatteryPerformanceModel batteryData) {
    // For multiple vehicles, show the original percentage breakdown
    return ListView.builder(
      itemCount: batteryData.health!.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (BuildContext context, int index) {
        final entry = batteryData.health!.entries.elementAt(index);
        return HeaderTrailerTextRow(
          header: entry.key,
          trailer: "${entry.value.count} (${entry.value.percentage}%)",
          textColor: Colors.white,
          fontSize: 14,
          onTap: () {
            _showAlertDialog(context, entry.value.imei);
          },
        );
      },
    );
  }

  List<Widget> _buildPerformanceCards(
      BuildContext context, LeadershipPerformanceState state) {
    final batteryData = state.batteryPerformance;

    // If no battery data available, show message
    if (batteryData == null) {
      return [
        Center(
          child: Text(
            leadershipText['no_battery_data']!,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      ];
    }

    List<Widget> cards = [];

    // Alerts card - always show, with "No alerts" message if empty
    final alertsData = batteryData.alerts;
    final hasAlerts = alertsData != null && alertsData.isNotEmpty;

    cards.add(
      ReusableSummaryCard(
        title: leadershipText['alerts_title']!,
        icon: vehicleDetailScreenImages["ic_warn"]!,
        items: hasAlerts
            ? alertsData.entries
                .map((entry) => {
                      "header": entry.key,
                      "trailer": "${entry.value.totalCount}",
                    })
                .toList()
            : [
                {
                  "header": leadershipText['no_alert_data']!,
                  "trailer": "",
                }
              ],
        backgroundColor: lightGray,
        onItemTap: hasAlerts
            ? (header, trailer) {
                final alertItem = alertsData[header];
                if (alertItem != null) {
                  _showAlertDialog(
                      context,
                      alertItem.topVehicles
                          .map((v) => v.vehImei ?? 'Unknown')
                          .toList());
                }
              }
            : null, // Disable tap for "No alerts"
      ),
    );
    cards.add(const SizedBox(height: 16));

    // Alarms card - always show, with "No alarms" message if empty
    final alarmsData = batteryData.alarms;
    final hasAlarms = alarmsData != null && alarmsData.isNotEmpty;

    cards.add(
      ReusableSummaryCard(
        title: leadershipText['alarms_title']!,
        icon: vehicleDetailScreenImages["ic_siren"]!,
        items: hasAlarms
            ? alarmsData.entries
                .map((entry) => {
                      "header": entry.key,
                      "trailer": "${entry.value.totalCount}",
                    })
                .toList()
            : [
                {
                  "header": leadershipText['no_alarm_data']!,
                  "trailer": "",
                }
              ],
        backgroundColor: lightGray,
        onItemTap: hasAlarms
            ? (header, trailer) {
                final alarmItem = alarmsData[header];
                if (alarmItem != null) {
                  _showAlertDialog(
                      context,
                      alarmItem.topVehicles
                          .map((v) => v.vehImei ?? 'Unknown')
                          .toList());
                }
              }
            : null, // Disable tap for "No alarms"
      ),
    );

    return cards;
  }
}
