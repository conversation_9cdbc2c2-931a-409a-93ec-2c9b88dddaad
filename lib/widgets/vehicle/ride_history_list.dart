import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/core/features/feature_service.dart';
import 'package:nds_app/models/ride_activity.dart';
import 'package:nds_app/widgets/vehicle/add_ride_activity_screen.dart';

import '../../common/constant.dart';
import '../../common/image_urls.dart';
import '../../common/strings.dart';
import '../../constant/vehicle_status.dart';

class RideHistoryList extends StatefulWidget {
  const RideHistoryList({super.key});

  @override
  State<RideHistoryList> createState() => _RideHistoryListState();
}

class _RideHistoryListState extends State<RideHistoryList> {
  // Mock ride data - will be replaced with API data in future
  final List<Map<String, dynamic>> rides = [
    {'id': 1, 'time': '6:30AM', 'duration': '1hr 20min', 'distance': '11kms'},
    {'id': 2, 'time': '8:30AM', 'duration': '1hr 20min', 'distance': '11kms'},
    {'id': 3, 'time': '6:30AM', 'duration': '1hr 20min', 'distance': '11kms'},
    {'id': 4, 'time': '6:30AM', 'duration': '1hr 20min', 'distance': '11kms'},
    {'id': 5, 'time': '6:30AM', 'duration': '1hr 20min', 'distance': '11kms'},
  ];

  // List to store ride activity objects
  final List<RideActivity> rideActivities = [];

  // Flag to control whether to show ride history list or add ride activity form
  bool _showAddRideActivity = false;

  // Store selected ride details for passing to add ride screen
  Map<String, dynamic> _selectedRideDetails = {};

  // Mock stats data - will be replaced with API data in future
  final Map<String, dynamic> todayStats = {
    'distance': '25 kms',
    'hours': '2 hrs',
    'count': 3,
  };

  void _toggleAddRideActivity() {
    setState(() {
      _showAddRideActivity = !_showAddRideActivity;

      // Clear selected ride details when closing the add screen
      if (!_showAddRideActivity) {
        _selectedRideDetails = {};
      }
    });
  }

  void _handleRideAdded(RideActivity rideActivity) {
    setState(() {
      rideActivities.add(rideActivity);
      todayStats['count'] = todayStats['count'] + 1;

      // Add a new ride to the rides list for UI display
      rides.insert(0, {
        'id': rides.length + 1,
        'time':
            DateFormat('h:mma').format(rideActivity.startTime).toLowerCase(),
        'duration': rideActivity.formattedDuration,
        'distance': 'N/A' // The UI will display this based on user input
      });

      // Toggle back to ride history view
      _showAddRideActivity = false;
    });

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Ride activity added successfully'),
        backgroundColor: colorSuccessGreen500,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return Stack(
      children: [
        Visibility(
          visible: currentVehicleStatus == VehicleStatus.connected &&
              FeatureService.shouldShowTodaysRidesCard() &&
              !_showAddRideActivity,
          child: SizedBox(
            height: dimensions.height,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 20 / 896 * dimensions.height),
                  // Today's Rides Stats Section
                  _buildTodaysRidesCard(dimensions),
                  // Ride History Title
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 10 / 414 * dimensions.width,
                      vertical: 8 / 896 * dimensions.height,
                    ),
                    margin: EdgeInsets.only(
                      bottom: 16,
                      left: 6 / 414 * dimensions.width,
                      right: 6 / 414 * dimensions.width,
                    ),
                    decoration: BoxDecoration(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        borderRadius: BorderRadius.circular(8.0),
                        border: Border.all(width: 1, color: colorGrey200),
                        boxShadow: [
                          BoxShadow(
                              color: colorBlack.withOpacity(0.25),
                              offset: const Offset(1, 3),
                              blurRadius: 3,
                              spreadRadius: 1),
                          BoxShadow(
                              color: colorWhite.withOpacity(0.25),
                              offset: const Offset(-1, -3),
                              blurRadius: 3,
                              spreadRadius: 1)
                        ]),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          alignment: Alignment.topLeft,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              insightsText['text23']!,
                              style: Theme.of(context).textTheme.headlineLarge,
                            ),
                          ),
                        ),
                        SizedBox(height: 16 / 896 * dimensions.height),
                        ...rides
                            .map((ride) => _buildRideCard(dimensions, ride)),
                        // Add some space at the bottom
                        SizedBox(height: 20 / 896 * dimensions.height),
                      ],
                    ),
                  ),
                  SizedBox(height: 160 / 896 * dimensions.height),
                ],
              ),
            ),
          ),
        ),

        // Add Ride Activity Screen
        Visibility(
          visible: _showAddRideActivity &&
              currentVehicleStatus == VehicleStatus.connected &&
              FeatureService.shouldShowAddRideActivity(),
          child: AddRideActivityScreen(
            onRideAdded: _handleRideAdded,
            onCancel: _toggleAddRideActivity,
            rideDetails: _selectedRideDetails,
          ),
        ),

        Visibility(
          visible: currentVehicleStatus == VehicleStatus.disconnected &&
              FeatureService.shouldShowDisconnectedMessage(),
          child: Center(
              child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Text(
              insightsText['text28']!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          )),
        ),
      ],
    );
  }

  Widget _buildTodaysRidesCard(Dimensions dimensions) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 12 / 414 * dimensions.width,
        vertical: 16 / 896 * dimensions.height,
      ),
      margin: EdgeInsets.only(
        bottom: 12,
        left: 6 / 414 * dimensions.width,
        right: 6 / 414 * dimensions.width,
      ),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1, color: colorGrey200),
          boxShadow: [
            BoxShadow(
                color: colorBlack.withOpacity(0.25),
                offset: const Offset(1, 3),
                blurRadius: 3,
                spreadRadius: 1),
            BoxShadow(
                color: colorWhite.withOpacity(0.25),
                offset: const Offset(-1, -3),
                blurRadius: 3,
                spreadRadius: 1)
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            insightsText['text24']!,
            style: Theme.of(context).textTheme.headlineLarge,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 12 / 896 * dimensions.height),
          // Stats row
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 8 / 414 * dimensions.width,
              vertical: 8 / 896 * dimensions.height,
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(width: 1, color: colorGrey300),
            ),
            child: IntrinsicHeight(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                      child: _buildStatColumn(
                          insightsText['text25']!, todayStats['distance'])),
                  VerticalDivider(
                    thickness: 1,
                    color: colorGrey300,
                  ),
                  Expanded(
                      child: _buildStatColumn(
                          insightsText['text26']!, todayStats['hours'])),
                  VerticalDivider(
                    thickness: 1,
                    color: colorGrey300,
                  ),
                  Expanded(
                    child: _buildStatColumn(insightsText['text27']!,
                        todayStats['count'].toString()),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 8 / 896 * dimensions.height),
        ],
      ),
    );
  }

  Widget _buildStatColumn(String title, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: Center(
            child: Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ),
        ),
        SizedBox(height: 4),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).primaryTextTheme.titleSmall,
          ),
        ),
      ],
    );
  }

  Widget _buildRideCard(Dimensions dimensions, Map<String, dynamic> ride) {
    return GestureDetector(
      onTap: () => _navigateToAddRideWithDetails(ride),
      child: Container(
        padding: EdgeInsets.all(8 / 414 * dimensions.width),
        margin: EdgeInsets.only(
          bottom: 16,
          left: 6 / 414 * dimensions.width,
          right: 6 / 414 * dimensions.width,
        ),
        decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(width: 1, color: colorGrey200),
            boxShadow: [
              BoxShadow(
                  color: colorBlack.withOpacity(0.25),
                  offset: const Offset(1, 3),
                  blurRadius: 3,
                  spreadRadius: 1),
              BoxShadow(
                  color: colorWhite.withOpacity(0.25),
                  offset: const Offset(-1, -3),
                  blurRadius: 3,
                  spreadRadius: 1)
            ]),
        child: Padding(
          padding: EdgeInsets.all(4 / 414 * dimensions.width),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Ride #${ride['id']}',
                    style: Theme.of(context).textTheme.displayMedium,
                  ),
                  InkWell(
                    onTap: () => _navigateToAddRideWithDetails(ride),
                    child: Text(
                      'Add',
                      style: TextStyle(
                        color: loginThemeColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        decoration: TextDecoration.underline,
                        decorationColor: loginThemeColor,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 4 / 896 * dimensions.height),
              Text(
                'Today · ${ride['time']}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              SizedBox(height: 8 / 896 * dimensions.height),
              // Chips row
              Row(
                children: [
                  _buildInfoChip(
                      rideHistoryScreenImages["rideTime"]!, ride['duration']),
                  SizedBox(width: 8 / 414 * dimensions.width),
                  _buildInfoChip(rideHistoryScreenImages["rideDistance"]!,
                      ride['distance'],
                      iconColor: colorSuccessGreen600),
                  SizedBox(width: 8 / 414 * dimensions.width),
                  SizedBox(
                    height: 28, // Match height of info chips
                    child: ElevatedButton(
                      onPressed: () => _navigateToAddRideWithDetails(ride),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: loginThemeColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        minimumSize: Size(0, 28),
                      ),
                      child: Text(
                        'Add',
                        style: TextStyle(
                          color: colorWhite,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(String icon, String label, {Color? iconColor}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorGrey300, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            icon,
            height: 15,
            width: 15,
            fit: BoxFit.fill,
            color: iconColor,
          ),
          SizedBox(width: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.displaySmall,
          ),
        ],
      ),
    );
  }

  // Method to navigate to add ride activity with pre-filled details
  void _navigateToAddRideWithDetails(Map<String, dynamic> ride) {
    setState(() {
      _showAddRideActivity = true;
      _selectedRideDetails = ride;
    });
  }
}
