import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:nds_app/core/features/feature_service.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/api_urls.dart';
import 'package:nds_app/models/trip.dart';
import 'package:nds_app/services/api_service.dart';

class CommonTripHistoryWidgets {
  /// Determines if pagination should be enabled based on company type
  /// Only ProdRed users get pagination functionality
  static bool shouldEnablePagination() {
    return FeatureService.shouldEnablePagination();
  }

  /// Loads trip data based on company configuration
  /// ProdRed users get paginated API calls, others get full data
  static Future<Map<String, dynamic>> loadTripData({
    required int currentPage,
    required int pageSize,
    bool isLoadMore = false,
  }) async {
    http.Response response;
    bool hasMoreData = true;

    if (FeatureService.shouldEnablePagination()) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String imei = prefs.getString(connectedVehicleImeiNo) ?? "";
      response =
          await BackendApi.initiateGetCall(ApiUrls.tripsVehicle, params: {
        "vIdVal": imei,
        "page": currentPage.toString(),
        "size": pageSize.toString(),
        "sort": ''
      });
    } else {
      response = await BackendApi.initiateGetCall(ApiUrls.tripHistory);
      // For non-ProdRedUser, we don't need pagination
      hasMoreData = false;
    }

    JsonDecoder decoder = const JsonDecoder();
    List<dynamic> jsonList = decoder.convert(response.body);
    List<Trip> trips = jsonList.map((e) => Trip.fromJson(e)).toList();

    // Check if there's more data for pagination-enabled users
    if (FeatureService.shouldEnablePagination() && trips.isEmpty) {
      hasMoreData = false;
    }

    return {
      'trips': trips,
      'hasMoreData': hasMoreData,
    };
  }

  /// Determines if the add ride activity should be shown
  /// Only available for ProdRed users
  static bool shouldShowAddRideActivity() {
    return FeatureService.shouldShowAddRideActivity();
  }

  /// Checks if disconnected message should be shown
  /// Only for ProdRed users when vehicle is disconnected
  static bool shouldShowDisconnectedMessage() {
    return FeatureService.shouldShowDisconnectedMessage();
  }

  /// Gets the disconnected message widget for ProdRed users
  static Widget getDisconnectedMessageWidget(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Text(
          insightsText['text28']!,
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Determines if today's rides card should be visible
  /// Only for ProdRed users
  static bool shouldShowTodaysRidesCard() {
    return FeatureService.shouldShowTodaysRidesCard();
  }

  /// Determines if the title should be visible
  /// Only for B2C and Lapa users when there's trip history
  static bool shouldShowTitle(bool hasTripHistory) {
    return FeatureService.shouldShowTripHistoryTitle(
        hasTripHistory: hasTripHistory);
  }

  /// Gets the title widget for B2C/Lapa users
  static Widget getTitleWidget(BuildContext context, double height) {
    return Column(
      children: [
        Text(
          textAlign: TextAlign.start,
          insightsText['text1']!,
          style: Theme.of(context).textTheme.headlineLarge,
        ),
        SizedBox(height: height),
      ],
    );
  }

  /// Determines if ProdRed-specific UI layout should be used
  static bool shouldUseProdRedLayout() {
    return FeatureService.shouldUseEnterpriseLayout();
  }

  /// Checks if the current company is B2C or Lapa
  static bool isB2COrLapaCompany() {
    return FeatureService.shouldUseConsumerLayout();
  }

  /// Checks if the current company is ProdRed
  static bool isProdRedCompany() {
    return FeatureService.shouldUseEnterpriseLayout();
  }

  /// Gets loading indicator with pagination support
  static Widget getLoadingIndicator(BuildContext context, bool isLoadingMore) {
    if (isLoadingMore) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  /// Gets end of data message
  static Widget getEndOfDataWidget(
      BuildContext context, bool hasMoreData, bool hasTripHistory) {
    if (!hasMoreData && hasTripHistory) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: Text(
            insightsText['text48']!,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
