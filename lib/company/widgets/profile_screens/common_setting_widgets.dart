import 'package:flutter/material.dart';
import 'package:nds_app/core/features/feature_service.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/widgets/profile/select_home_display_name.dart';
import 'package:nds_app/widgets/profile/select_vehcile_visibility_range.dart';

class CommonSettingWidgets {
  /// Gets the vehicle visibility range widget based on company configuration
  /// Only visible for non-B2C and non-Lapa users
  static Widget getVehicleVisibilityRangeWidget(
    BuildContext context,
    int selectRange,
    Function(int) onRangeSelect,
  ) {
    return FeatureService.conditionalWidget(
      child: SelectVehicleVisibilityRange(
        selectedRange: selectRange,
        onRangeSelect: onRangeSelect,
      ),
      condition: () => FeatureService.shouldShowVehicleVisibilityRange(),
    );
  }

  /// Gets the home display name widget based on company configuration
  /// Only visible for B2C and Lapa users
  static Widget getHomeDisplayNameWidget(
    BuildContext context,
    Dimensions dimensions,
    String selectHomeDisplayName,
    Function(String) onHomeDisplayNameSelect,
  ) {
    return FeatureService.conditionalWidget(
      child: Column(
        children: [
          SizedBox(
            height: 14 / 896 * dimensions.height,
          ),
          SelectHomeDisplayName(
            selectedHomeDisplayName: selectHomeDisplayName,
            onHomeDisplayNameSelect: onHomeDisplayNameSelect,
          ),
        ],
      ),
      condition: () => FeatureService.shouldShowHomeDisplayNameSettings(),
    );
  }

  /// Checks if the current company is B2C or Lapa
  static bool isB2COrLapaCompany() {
    return FeatureService.shouldUseConsumerLayout();
  }
}
