import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import 'package:flutter/services.dart' show rootBundle;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:location/location.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/core/features/feature_service.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/common/text_styles.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/nearby_vehicle.dart';
import 'package:nds_app/services/api_service.dart';
import 'package:nds_app/services/location_service.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/streams/nearby_poi_data.dart';
import 'package:nds_app/streams/user_location_data.dart';
import 'package:nds_app/utils/device_battery_image.dart';
import 'package:nds_app/utils/date_time_extenstion.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:ui' as ui;
import 'package:url_launcher/url_launcher.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../constant/api_urls.dart';
import '../../../streams/map_and_available_vehicle_stream.dart';
import 'package:nds_app/streams/leadership_status_stream.dart';
import 'package:nds_app/streams/leadership_combined_stream.dart';
import 'package:nds_app/models/leadership_status.dart';
import 'package:nds_app/models/leadership_metadata.dart';
import 'package:nds_app/repository/leadership_status_repository.dart';
import 'package:nds_app/repository/leadership_metadata_repository.dart';
import 'package:nds_app/utils/time_filter_utils.dart';
import '../../../utils/toast.dart';
import 'package:nds_app/utils/api_error_handler.dart';

class MapAndAvailableVehicle extends StatefulWidget {
  const MapAndAvailableVehicle({super.key});

  @override
  State<MapAndAvailableVehicle> createState() => _MapAndAvailableVehicleState();
}

class _MapAndAvailableVehicleState extends State<MapAndAvailableVehicle> {
  final Completer<GoogleMapController> _controller =
      Completer<GoogleMapController>();
  UserLocationDataStream userLocationStream = UserLocationDataStream();
  NearbyPOIDataStream nearByPOIStream = NearbyPOIDataStream();
  final MapAndAvailableVehicleStream selectedVehicleStream =
      MapAndAvailableVehicleStream();
  late NearbyPOI nearbyPOI;
  late int selectedRange;

  Set<Marker> markers = {};
  List<NearByVehicle> nearbyVehicles = [];
  bool isNavigated = false;
  late Color? vehicleColor;
  late LatLng _point1;
  late LatLng _point2;
  double _currentZoom = 14.474599838256836;
  Timer? _zoomLogTimer;
  LogScreenTrackingEvent logScreenTrackingEvent = LogScreenTrackingEvent();
  bool _hasManualSelection =
      false; // Flag to track if user has manually selected a vehicle
  // Leadership filter dropdown
  final List<String> _vehicleFilters = const [
    'Running',
    'Trips',
    'Alerts',
    'Alarms'
  ];
  String _selectedFilter = 'Running';
  LeadershipStatusResponse? _currentStatusData; // Store current API response
  List<LeadershipStatusItem> _currentFilteredList = []; // Current filtered list
  LeadershipStatusItem?
      _selectedLeadershipVehicle; // Currently selected leadership vehicle
  final StreamController<LeadershipStatusItem?>
      _selectedLeadershipVehicleController =
      StreamController<LeadershipStatusItem?>.broadcast();
  final StreamController<Set<Marker>> _markersController =
      StreamController<Set<Marker>>.broadcast();
  final LeadershipStatusStream _leadershipStatusStream =
      LeadershipStatusStream();
  final LeadershipCombinedStream _leadershipCombinedStream =
      LeadershipCombinedStream();

  @override
  void initState() {
    _point1 = const LatLng(0, 0);
    _point2 = const LatLng(0, 0);

    nearbyPOI = currentNearbyPOI;
    markers = currentMarkers;
    loadColorFromSharedPreferences();
    userLocationStream.userLocationData.listen((event) {
      userLocationData = event;
    });

    // Initialize selected vehicle stream with null to ensure proper state
    selectedVehicleStream.updateSelectedVehicle(null);

    // Reset manual selection flag when vehicle status changes
    // This allows auto-selection to work again when user reconnects
    if (currentVehicleStatus == VehicleStatus.disconnected) {
      _hasManualSelection = false;
    }

    // Listen to nearby vehicles stream to auto-select connected vehicle when data is available
    nearByPOIStream.nearbyPOI.listen((nearbyPOIData) {
      // Only auto-select if user hasn't manually selected a vehicle
      if (!_hasManualSelection &&
          vehicleInfoConstant != null &&
          currentVehicleStatus == VehicleStatus.connected) {
        // Find the connected vehicle in nearby vehicles and auto-select it
        if (nearbyPOIData.nearByVehicles != null) {
          for (var vehicle in nearbyPOIData.nearByVehicles!) {
            if (vehicleInfoConstant?.regNo == (vehicle.regNo ?? "")) {
              selectedVehicleStream.updateSelectedVehicle(vehicle);
              break;
            }
          }
        }
      }
    });

    // Listen to selected vehicle stream to reset manual selection flag when cleared
    selectedVehicleStream.selectedVehicle.listen((selectedVehicle) {
      if (selectedVehicle == null) {
        _hasManualSelection = false;
      }
    });

    // Load initial leadership status data if user is in leadership role
    if (FeatureService.hasLeadershipRole()) {
      _loadInitialLeadershipData();
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return FutureBuilder(
      builder: (context, snapshot) {
        Widget widget = Center(
          child: Image.asset(
            isTwoWheels
                ? loaderGifImages['2Wheels']!
                : loaderGifImages['3Wheels']!,
          ),
        );
        if (snapshot.connectionState == ConnectionState.done) {
          widget = Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Visibility(
                visible: FeatureService.shouldShowVehicleVisibilityRange(),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      homeScreenText["text2"]!,
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    Text(
                      homeScreenText["text18"]!
                          .replaceAll("@range", selectedRange.toString()),
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                  ],
                ),
              ),
              // Leadership dropdown filter (ProdRed leadership only)
              Visibility(
                visible: FeatureService.hasLeadershipRole(),
                child: Container(
                  width: 374 / 414 * dimensions.width,
                  margin: EdgeInsets.only(top: 4 / 896 * dimensions.height),
                  padding: EdgeInsets.symmetric(
                    horizontal: 16 / 414 * dimensions.width,
                    vertical: 8 / 896 * dimensions.height,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(color: colorGrey300, width: 1),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      isExpanded: true,
                      value: _selectedFilter,
                      icon: Icon(
                        Icons.keyboard_arrow_down,
                        color: Theme.of(context).textTheme.bodyLarge?.color ??
                            Colors.black87,
                        size: 24,
                      ),
                      selectedItemBuilder: (BuildContext context) {
                        return _vehicleFilters.map<Widget>((String item) {
                          return Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              item,
                              style: poppinsTextStyle(
                                14 / 414 * dimensions.width,
                                Theme.of(context).textTheme.bodyLarge?.color ??
                                    Colors.black87,
                                FontWeight.w600,
                              ),
                            ),
                          );
                        }).toList();
                      },
                      items: _vehicleFilters.asMap().entries.map((entry) {
                        String e = entry.value;
                        bool isSelected = e == _selectedFilter;
                        return DropdownMenuItem<String>(
                          value: e,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(vertical: 8),
                                child: Text(
                                  e,
                                  style: poppinsTextStyle(
                                    14 / 414 * dimensions.width,
                                    isSelected
                                        ? (Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.white
                                            : Colors.black)
                                        : (Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.grey.shade400
                                            : Colors.grey.shade600),
                                    isSelected
                                        ? FontWeight.w600
                                        : FontWeight.w400,
                                  ),
                                ),
                              ),
                              Container(
                                height: 0.5,
                                width: double.infinity,
                                color: colorGrey300,
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedFilter = value;
                            _updateFilteredList(); // Update the filtered list
                          });
                          debugPrint('Leadership filter changed to: $value');
                        }
                      },
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 8 / 896 * dimensions.height,
              ),
              Stack(
                children: [
                  Container(
                    height: 380 / 896 * dimensions.height,
                    width: 374 / 414 * dimensions.width,
                    decoration: BoxDecoration(
                        border: Border.all(width: 1, color: colorGrey300),
                        borderRadius: BorderRadius.circular(8.0),
                        boxShadow: [
                          BoxShadow(
                              color: MyApp.of(context).getCurrentThemeMode() ==
                                      ThemeMode.dark
                                  ? colorGrey500.withOpacity(0.7)
                                  : colorGrey300.withOpacity(0.7),
                              offset: const Offset(3, 3),
                              spreadRadius: 2,
                              blurRadius: 5)
                        ]),
                    child: StreamBuilder<NearbyPOI>(
                        stream: nearByPOIStream.nearbyPOI,
                        builder: (context, snapshot) {
                          if (snapshot.data != null) {
                            nearbyPOI = snapshot.data!;
                          }
                          return ClipRRect(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(8)),
                            child: FeatureService.hasLeadershipRole()
                                ? StreamBuilder<Set<Marker>>(
                                    stream: _markersController.stream,
                                    initialData: markers,
                                    builder: (context, markerSnapshot) {
                                      return GoogleMap(
                                        mapType: MapType.normal,
                                        zoomControlsEnabled: false,
                                        myLocationButtonEnabled: false,
                                        initialCameraPosition: CameraPosition(
                                          target: LatLng(
                                              userLocationData?.latitude ?? 0,
                                              userLocationData?.longitude ?? 0),
                                          zoom: _currentZoom,
                                        ),
                                        myLocationEnabled: true,
                                        markers: markerSnapshot.data ?? markers,
                                        onMapCreated:
                                            (GoogleMapController controller) {
                                          _controller.complete(controller);
                                        },
                                        onCameraMove: (position) {
                                          _onCameraMove(position);
                                        },
                                        gestureRecognizers: <Factory<
                                            OneSequenceGestureRecognizer>>{
                                          Factory<OneSequenceGestureRecognizer>(
                                            () => EagerGestureRecognizer(),
                                          ),
                                        },
                                      );
                                    },
                                  )
                                : GoogleMap(
                                    mapType: MapType.normal,
                                    zoomControlsEnabled: false,
                                    myLocationButtonEnabled: false,
                                    initialCameraPosition: CameraPosition(
                                      target: LatLng(
                                          userLocationData?.latitude ?? 0,
                                          userLocationData?.longitude ?? 0),
                                      zoom: _currentZoom,
                                    ),
                                    myLocationEnabled: true,
                                    markers: markers,
                                    onMapCreated:
                                        (GoogleMapController controller) {
                                      _controller.complete(controller);
                                    },
                                    onCameraMove: (position) {
                                      _onCameraMove(position);
                                    },
                                    gestureRecognizers: <Factory<
                                        OneSequenceGestureRecognizer>>{
                                      Factory<OneSequenceGestureRecognizer>(
                                        () => EagerGestureRecognizer(),
                                      ),
                                    },
                                  ),
                          );
                        }),
                  ),
                  Positioned(
                    bottom: 16,
                    right: 16,
                    child: Column(
                      children: [
                        // Location button
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(24),
                              onTap: () async {
                                GoogleMapController controller =
                                    await _controller.future;
                                await controller.animateCamera(
                                    CameraUpdate.newCameraPosition(
                                        CameraPosition(
                                  target: LatLng(
                                      userLocationData?.latitude ?? 0,
                                      userLocationData?.longitude ?? 0),
                                  zoom: 14.4746,
                                )));
                                nearByPOIStream
                                    .updateNearbyVehiclesResponse(nearbyPOI);
                              },
                              child: const Icon(
                                Icons.my_location_outlined,
                                color: Colors.black87,
                                size: 24,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        // Navigation button
                        StreamBuilder<NearByVehicle?>(
                          stream: selectedVehicleStream.selectedVehicle,
                          builder: (context, snapshot) {
                            // Show navigation icon when there's a selected vehicle
                            // For B2C/Lapa: Always show when connected
                            // For other companies: Show when any vehicle is selected (connected or manual selection)
                            bool shouldShowNavigation = snapshot.hasData &&
                                snapshot.data != null &&
                                snapshot.data!.regNo != null &&
                                snapshot.data!.regNo!.isNotEmpty;

                            if (shouldShowNavigation) {
                              return Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: const Color(0xff3871E1),
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 4,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(24),
                                    onTap: () {
                                      openGoogleMaps(
                                        userLat:
                                            userLocationData?.latitude ?? 0,
                                        userLong:
                                            userLocationData?.longitude ?? 0,
                                        destLat: snapshot.data?.latitude ?? 0,
                                        destLong: snapshot.data?.longitude ?? 0,
                                      );
                                    },
                                    child: const Icon(
                                      Icons.directions,
                                      color: Colors.white,
                                      size: 30,
                                    ),
                                  ),
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                        // Leadership Navigation button
                        if (FeatureService.hasLeadershipRole())
                          StreamBuilder<LeadershipStatusItem?>(
                            stream: _selectedLeadershipVehicleController.stream,
                            builder: (context, snapshot) {
                              if (snapshot.hasData && snapshot.data != null) {
                                return Column(
                                  children: [
                                    const SizedBox(height: 12),
                                    Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: const Color(0xff3871E1),
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.2),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Material(
                                        color: Colors.transparent,
                                        child: InkWell(
                                          borderRadius:
                                              BorderRadius.circular(24),
                                          onTap: () {
                                            openGoogleMaps(
                                              userLat:
                                                  userLocationData?.latitude ??
                                                      0,
                                              userLong:
                                                  userLocationData?.longitude ??
                                                      0,
                                              destLat:
                                                  snapshot.data?.latitude ?? 0,
                                              destLong:
                                                  snapshot.data?.longitude ?? 0,
                                            );
                                          },
                                          child: const Icon(
                                            Icons.directions,
                                            color: Colors.white,
                                            size: 30,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 16 / 896 * dimensions.height,
              ),
              StreamBuilder(
                stream: FeatureService.hasLeadershipRole()
                    ? _leadershipCombinedStream.stream
                    : nearByPOIStream.nearbyPOI,
                builder: (context, snapshot) {
                  Widget widget = Center(
                    child: Image.asset(
                      isTwoWheels
                          ? loaderGifImages['2Wheels']!
                          : loaderGifImages['3Wheels']!,
                    ),
                  );
                  if (FeatureService.hasLeadershipRole()) {
                    final LeadershipCombinedData? data =
                        snapshot.data as LeadershipCombinedData?;
                    // Update stored data when new data arrives
                    if (data?.status != null) {
                      _currentStatusData = data!.status;
                      _updateFilteredList();
                    }
                    widget = _buildLeadershipVehicleListFromFilteredData(
                        context, dimensions);
                  } else if (snapshot.data != null) {
                    nearbyPOI = snapshot.data as NearbyPOI;
                    widget = getAvailableVehicles(dimensions, nearbyPOI);
                  } else if (nearbyPOI.nearByVehicles != null) {
                    widget = getAvailableVehicles(dimensions, nearbyPOI);
                  }
                  return widget;
                },
              ),
              SizedBox(
                height: 100 / 896 * dimensions.height,
              ),
            ],
          );
        }
        return widget;
      },
      future: getNearbyVehicles(),
    );
  }

  void _onCameraMove(CameraPosition position) {
    if (_currentZoom != position.zoom) {
      _currentZoom = position.zoom;
      _zoomLogTimer?.cancel();
      _zoomLogTimer = Timer(const Duration(seconds: 1), () {
        logScreenTrackingEvent.logScreenView(
            eventName: trackingLabels['ZoomMapAction']!);
      });
    }
  }

  void _setMapFitToBounds() async {
    final LatLngBounds bounds = LatLngBounds(
      southwest: LatLng(
        _point1.latitude < _point2.latitude
            ? _point1.latitude
            : _point2.latitude,
        _point1.longitude < _point2.longitude
            ? _point1.longitude
            : _point2.longitude,
      ),
      northeast: LatLng(
        _point1.latitude > _point2.latitude
            ? _point1.latitude
            : _point2.latitude,
        _point1.longitude > _point2.longitude
            ? _point1.longitude
            : _point2.longitude,
      ),
    );
    GoogleMapController controller = await _controller.future;

    controller.moveCamera(CameraUpdate.newLatLngBounds(bounds, 50));
  }

  getNearbyVehicles() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    selectedRange = pref.getInt(vehicleVisibilityRangeKey) ?? 10;
    userLocationData = userLocationData ?? await determineLocation();

    _point1 = LatLng(
        userLocationData!.latitude ?? 0, userLocationData!.longitude ?? 0);
    userLocationStream.updateUserlocationDataResponse(userLocationData!);

    // Do not call nearby-poi for ProdRed Leadership role
    if (FeatureService.hasLeadershipRole()) {
      // Clear any existing markers based on nearby-poi data
      markers.clear();
      return;
    }

    refreshNearByPOIData();
  }

  setMarkers(List<NearByVehicle> nearbyVehicles) async {
    try {
      final Uint8List disconnectedVehicleMarkerIcon =
          await getBytesFromAsset(googleMapImages['marker_red']!, null);
      for (NearByVehicle vehicle in nearbyVehicles) {
        try {
          Uint8List? connectedVehicleMarkerIcon;
          if (vehicleInfoConstant != null &&
              vehicleInfoConstant?.regNo == (vehicle.regNo ?? "")) {
            connectedVehicleMarkerIcon =
                await getBytesFromAsset(googleMapImages['marker_green']!, null);
          }

          // Ensure we have valid coordinates
          if (vehicle.latitude != null &&
              vehicle.longitude != null &&
              vehicle.latitude != 0 &&
              vehicle.longitude != 0) {
            markers.add(Marker(
              icon: BitmapDescriptor.bytes(
                  connectedVehicleMarkerIcon ?? disconnectedVehicleMarkerIcon,
                  width: 48,
                  height: 48),
              markerId: MarkerId(vehicle.regNo ?? DateTime.now().toString()),
              position: LatLng(vehicle.latitude!, vehicle.longitude!),
              infoWindow: InfoWindow(
                title: vehicle.regNo ?? "",
              ),
            ));
          }
        } catch (e) {
          debugPrint('Error setting marker for vehicle ${vehicle.regNo}: $e');
          // Continue with other vehicles
        }
      }
      currentMarkers = markers;
    } catch (e) {
      debugPrint('Error in setMarkers: $e');
      // Keep existing markers if there's an error
    }
  }

  Future<Uint8List> getBytesFromAsset(String path, int? width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  getAvailableVehicles(Dimensions dimensions, NearbyPOI nearbyPOI) {
    nearbyVehicles = nearbyPOI.nearByVehicles ?? [];

    return ListView.builder(
      padding: EdgeInsets.zero,
      physics: const BouncingScrollPhysics(),
      shrinkWrap: true,
      itemCount: nearbyVehicles.length,
      itemBuilder: (context, index) {
        NearByVehicle vehicleDetails = nearbyVehicles[index];
        DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(
            vehicleDetails.locationRecordedTime ?? 0);

        String? imageUrl;

        vehicleDetails.images?.forEach(
          (element) {
            if (element.tag == defaultImageTag) {
              imageUrl = element.url;
            }
          },
        );
        // Note: Auto-selection is now handled in initState to prevent overriding manual selections
        return GestureDetector(
          onTap: () async {
            // Mark that user has manually selected a vehicle
            _hasManualSelection = true;

            // Update selected vehicle
            selectedVehicleStream.updateSelectedVehicle(vehicleDetails);

            // Move camera to the selected vehicle's location
            try {
              // Wait for map controller to be ready
              if (!_controller.isCompleted) {
                // If controller is not ready, try again after a short delay
                Future.delayed(const Duration(milliseconds: 500), () async {
                  try {
                    GoogleMapController controller = await _controller.future;
                    LatLng targetLocation = LatLng(
                      vehicleDetails.latitude ?? 0,
                      vehicleDetails.longitude ?? 0,
                    );

                    // Ensure we have valid coordinates
                    if (targetLocation.latitude != 0 &&
                        targetLocation.longitude != 0) {
                      await controller.animateCamera(
                          CameraUpdate.newCameraPosition(CameraPosition(
                        target: targetLocation,
                        zoom: 14.4746,
                      )));
                    }
                  } catch (e) {
                    // Handle any controller errors
                  }
                });
                return;
              }

              GoogleMapController controller = await _controller.future;
              LatLng targetLocation = LatLng(
                vehicleDetails.latitude ?? 0,
                vehicleDetails.longitude ?? 0,
              );

              // Ensure we have valid coordinates
              if (targetLocation.latitude != 0 &&
                  targetLocation.longitude != 0) {
                await controller.animateCamera(
                    CameraUpdate.newCameraPosition(CameraPosition(
                  target: targetLocation,
                  zoom: 14.4746,
                )));
              }
            } catch (e) {
              // Handle any controller errors
            }

            nearByPOIStream.updateNearbyVehiclesResponse(nearbyPOI);
          },
          child: Column(
            children: [
              Container(
                height: 72 / 896 * dimensions.height,
                padding: EdgeInsets.symmetric(
                    horizontal: 12 / 414 * dimensions.width),
                decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: MyApp.of(context).getCurrentThemeMode() ==
                                ThemeMode.dark
                            ? [colorGrey800, colorBlack]
                            : [colorGrey200, colorWhite],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight),
                    borderRadius: BorderRadius.all(
                        Radius.circular(8 / 414 * dimensions.width)),
                    border: Border.all(
                        color: (vehicleInfoConstant != null &&
                                vehicleInfoConstant?.regNo ==
                                    (vehicleDetails.regNo ?? ""))
                            ? colorGrey600
                            : colorGrey300),
                    boxShadow: [
                      BoxShadow(
                          color: MyApp.of(context).getCurrentThemeMode() ==
                                  ThemeMode.dark
                              ? colorGrey500.withOpacity(0.7)
                              : colorGrey300.withOpacity(0.7),
                          offset: const Offset(3, 3),
                          spreadRadius: 2,
                          blurRadius: 5)
                    ]),
                child: Row(
                  children: [
                    SizedBox(
                      width: 50 / 414 * dimensions.width,
                      child: imageUrl != null
                          ? CachedNetworkImage(
                              imageUrl: imageUrl!,
                              fit: BoxFit.contain,
                              memCacheWidth: 150,
                              maxWidthDiskCache: 150,
                              fadeInDuration: const Duration(milliseconds: 100),
                              placeholder: (context, url) => const SizedBox(
                                width: 20,
                                height: 20,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2.0),
                              ),
                              errorWidget: (context, url, error) => const Icon(
                                  Icons.electric_bike,
                                  size: 50,
                                  color: Colors.grey),
                            )
                          : const Icon(Icons.electric_bike,
                              size: 50, color: Colors.grey),
                    ),
                    SizedBox(
                      width: 8 / 414 * dimensions.width,
                    ),
                    SizedBox(
                      width: 285 / 414 * dimensions.width,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                vehicleDetails.regNo ?? "",
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              getBatteryPercentageContainer(dimensions,
                                  vehicleDetails.charge ?? 0, context),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "${vehicleDetails.distance ?? 0} ${vehicleDetails.distanceUnit}",
                                style: poppinsTextStyle(
                                    12 / 414 * dimensions.width,
                                    colorGrey400,
                                    FontWeight.w300),
                              ),
                              Visibility(
                                visible: (vehicleInfoConstant != null &&
                                    vehicleInfoConstant?.regNo ==
                                        (vehicleDetails.regNo ?? "")),
                                child: Text(
                                  homeScreenText['connected_label']!,
                                  style: poppinsTextStyle(
                                      12 / 414 * dimensions.width,
                                      colorGreenSuccess,
                                      FontWeight.w300),
                                ),
                              ),
                              Text(
                                dateTime.timeAgo(numericDates: true),
                                style: poppinsTextStyle(
                                    12 / 414 * dimensions.width,
                                    colorGrey400,
                                    FontWeight.w300),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12 / 896 * dimensions.height)
            ],
          ),
        );
      },
    );
  }

  // Leadership list from locally filtered data
  Widget _buildLeadershipVehicleListFromFilteredData(
      BuildContext context, Dimensions dimensions) {
    if (_currentFilteredList.isEmpty) {
      return SizedBox(
        height: 100,
        child: Center(
          child: Text(
            'No $_selectedFilter found',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      );
    }

    return Column(
      children: _currentFilteredList
          .map((e) => _buildLeadershipStatusItem(context, dimensions, e))
          .toList(),
    );
  }

  Widget _buildLeadershipStatusItem(
      BuildContext context, Dimensions dimensions, LeadershipStatusItem v) {
    return GestureDetector(
      onTap: () async {
        // Select this vehicle (no setState to avoid screen refresh)
        _selectedLeadershipVehicle = v;
        _selectedLeadershipVehicleController.add(v);

        // Update markers to highlight selected vehicle
        await _setLeadershipVehicleMarkersWithSelection();

        // Move camera to the selected vehicle's location (similar to nearby POI)
        try {
          // Wait for map controller to be ready
          if (!_controller.isCompleted) {
            // If controller is not ready, try again after a short delay
            Future.delayed(const Duration(milliseconds: 500), () async {
              try {
                GoogleMapController controller = await _controller.future;
                LatLng targetLocation = LatLng(
                  v.latitude ?? 0,
                  v.longitude ?? 0,
                );

                // Ensure we have valid coordinates
                if (targetLocation.latitude != 0 &&
                    targetLocation.longitude != 0) {
                  await controller.animateCamera(
                      CameraUpdate.newCameraPosition(CameraPosition(
                    target: targetLocation,
                    zoom: 14.4746,
                  )));
                }
              } catch (e) {
                // Handle any controller errors
                debugPrint('Error moving camera (delayed): $e');
              }
            });
            return;
          }

          GoogleMapController controller = await _controller.future;
          LatLng targetLocation = LatLng(
            v.latitude ?? 0,
            v.longitude ?? 0,
          );

          // Ensure we have valid coordinates
          if (targetLocation.latitude != 0 && targetLocation.longitude != 0) {
            await controller
                .animateCamera(CameraUpdate.newCameraPosition(CameraPosition(
              target: targetLocation,
              zoom: 14.4746,
            )));
          }
        } catch (e) {
          // Handle any controller errors
          debugPrint('Error moving camera: $e');
        }
      },
      child: Container(
        margin: EdgeInsets.only(
          bottom: 16,
          left: 6 / 414 * dimensions.width,
          right: 6 / 414 * dimensions.width,
        ),
        padding: EdgeInsets.all(16 / 375 * dimensions.width),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(width: 1, color: colorGrey300),
        ),
        child: Row(
          children: [
            // Vehicle Image
            Container(
              width: 60 / 375 * dimensions.width,
              height: 60 / 375 * dimensions.width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                image: (v.images.isNotEmpty &&
                        (v.images.first.url ?? '').isNotEmpty)
                    ? DecorationImage(
                        image: CachedNetworkImageProvider(v.images.first.url!),
                        fit: BoxFit.cover,
                      )
                    : const DecorationImage(
                        image: AssetImage('assets/bikes/blue_bike.png'),
                        fit: BoxFit.cover,
                      ),
              ),
            ),
            SizedBox(width: 16 / 375 * dimensions.width),
            // Vehicle Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    v.modelNo ?? 'Squid Pro Max',
                    style: poppinsTextStyle(
                      18 / 414 * dimensions.width,
                      Theme.of(context).textTheme.bodyLarge?.color ??
                          Colors.black,
                      FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 4 / 896 * dimensions.height),
                  Text(
                    v.vehImei ?? 'IMEI',
                    style: poppinsTextStyle(
                      16 / 414 * dimensions.width,
                      (Theme.of(context).brightness == Brightness.dark)
                          ? Colors.grey.shade400
                          : colorGrey500,
                      FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _setLeadershipVehicleMarkers() async {
    try {
      // Clear existing markers
      markers.clear();

      if (_currentFilteredList.isEmpty) return;

      // Get marker icon (similar to nearby POI)
      final Uint8List leadershipVehicleMarkerIcon =
          await getBytesFromAsset(googleMapImages['marker_red']!, null);

      // Add markers for all vehicles in current filtered list
      for (LeadershipStatusItem vehicle in _currentFilteredList) {
        try {
          // Ensure we have valid coordinates
          if (vehicle.latitude != null &&
              vehicle.longitude != null &&
              vehicle.latitude != 0 &&
              vehicle.longitude != 0) {
            markers.add(Marker(
              icon: BitmapDescriptor.bytes(leadershipVehicleMarkerIcon,
                  width: 48, height: 48),
              markerId: MarkerId(vehicle.vehImei ?? DateTime.now().toString()),
              position: LatLng(vehicle.latitude!, vehicle.longitude!),
              infoWindow: InfoWindow(
                title: vehicle.modelNo ?? "Vehicle",
                snippet: vehicle.vehImei ?? 'Unknown',
              ),
            ));
          }
        } catch (e) {
          debugPrint(
              'Error setting marker for leadership vehicle ${vehicle.vehImei}: $e');
          // Continue with other vehicles
        }
      }

      // Update current markers (similar to nearby POI pattern)
      currentMarkers = markers;

      // Update markers through stream to avoid screen refresh
      _markersController.add(Set<Marker>.from(markers));
    } catch (e) {
      debugPrint('Error in _setLeadershipVehicleMarkers: $e');
      // Keep existing markers if there's an error
    }
  }

  Future<void> _setLeadershipVehicleMarkersWithSelection() async {
    try {
      // Clear existing markers
      markers.clear();

      if (_currentFilteredList.isEmpty) return;

      // Get marker icons (similar to nearby POI)
      final Uint8List normalMarkerIcon =
          await getBytesFromAsset(googleMapImages['marker_red']!, null);
      final Uint8List selectedMarkerIcon =
          await getBytesFromAsset('assets/google_map/marker_green.png', null);

      // Add markers for all vehicles in current filtered list
      for (LeadershipStatusItem vehicle in _currentFilteredList) {
        try {
          // Ensure we have valid coordinates
          if (vehicle.latitude != null &&
              vehicle.longitude != null &&
              vehicle.latitude != 0 &&
              vehicle.longitude != 0) {
            // Use green marker for selected vehicle, red for others
            bool isSelected =
                _selectedLeadershipVehicle?.vehImei == vehicle.vehImei;

            markers.add(Marker(
              icon: BitmapDescriptor.bytes(
                  isSelected ? selectedMarkerIcon : normalMarkerIcon,
                  width: 48,
                  height: 48),
              markerId: MarkerId(vehicle.vehImei ?? DateTime.now().toString()),
              position: LatLng(vehicle.latitude!, vehicle.longitude!),
              infoWindow: InfoWindow(
                title: vehicle.modelNo ?? "Vehicle",
                snippet: vehicle.vehImei ?? 'Unknown',
              ),
            ));
          }
        } catch (e) {
          debugPrint(
              'Error setting marker for leadership vehicle ${vehicle.vehImei}: $e');
          // Continue with other vehicles
        }
      }

      // Update current markers (similar to nearby POI pattern)
      currentMarkers = markers;

      // Update markers through stream to avoid screen refresh
      _markersController.add(Set<Marker>.from(markers));
    } catch (e) {
      debugPrint('Error in _setLeadershipVehicleMarkersWithSelection: $e');
      // Keep existing markers if there's an error
    }
  }

  Future<void> _loadInitialLeadershipData() async {
    try {
      // Load today's data by default
      final timeRange = TimeFilterUtils.calculateTimeRange(
          homeScreenText["text32"]!); // "Today"

      // Call both APIs in parallel
      final futures = await Future.wait([
        LeadershipMetadataRepository().getMetadata(
          startTime: timeRange['startTime']!,
          endTime: timeRange['endTime']!,
        ),
        LeadershipStatusRepository().getStatus(
          startTime: timeRange['startTime']!,
          endTime: timeRange['endTime']!,
        ),
      ]);

      final metadata = futures[0] as LeadershipMetadata?;
      final status = futures[1] as LeadershipStatusResponse?;

      // Update both streams
      _leadershipStatusStream.update(status);
      _leadershipCombinedStream.updateBoth(metadata, status);

      // Update local data for filtering
      if (status != null) {
        _currentStatusData = status;
        _updateFilteredList();
      }
    } catch (e) {
      debugPrint('Error loading initial leadership data: $e');
      // Don't show error to user for initial load
    }
  }

  void _updateFilteredList() {
    if (_currentStatusData == null) {
      _currentFilteredList = [];
      return;
    }

    switch (_selectedFilter) {
      case 'Running':
        _currentFilteredList = _currentStatusData!.running;
        break;
      case 'Trips':
        _currentFilteredList = _currentStatusData!.trips;
        break;
      case 'Alerts':
        _currentFilteredList = _currentStatusData!.alerts;
        break;
      case 'Alarms':
        _currentFilteredList = _currentStatusData!.alarms;
        break;
      default:
        _currentFilteredList = _currentStatusData!.running;
    }
    debugPrint(
        'Filtered list updated: ${_currentFilteredList.length} items for $_selectedFilter');

    // Clear selected vehicle if it's not in the new filtered list
    if (_selectedLeadershipVehicle != null) {
      bool isSelectedVehicleInList = _currentFilteredList.any(
          (vehicle) => vehicle.vehImei == _selectedLeadershipVehicle?.vehImei);
      if (!isSelectedVehicleInList) {
        _selectedLeadershipVehicle = null;
        _selectedLeadershipVehicleController.add(null);
      }
    }

    // Update markers on map for leadership users
    if (FeatureService.hasLeadershipRole()) {
      if (_selectedLeadershipVehicle != null) {
        _setLeadershipVehicleMarkersWithSelection();
      } else {
        _setLeadershipVehicleMarkers();
      }
    }
  }

  loadColorFromSharedPreferences() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    String hexColorInStr = pref.getString(vehicleThemeColorInHex) ?? "";
    vehicleColor =
        hexColorInStr.isNotEmpty ? hexColorInStr.toColor() : colorGrey800;
  }

  Future<void> refreshNearByPOIData() async {
    JsonDecoder decoder = const JsonDecoder();

    while (!isNavigated) {
      // Skip polling when user is in leadership role
      if (FeatureService.hasLeadershipRole()) {
        return;
      }
      try {
        // Get user location
        userLocationData = await determineLocation();

        // Validate location data
        double? latitude = userLocationData?.latitude;
        double? longitude = userLocationData?.longitude;

        // Additional validation to prevent 0 values
        if (latitude == null ||
            latitude == 0 ||
            longitude == null ||
            longitude == 0) {
          //CustomToast.error('Invalid location detected, using default coordinates');
          // Use fallback coordinates
          latitude = 28.612894;
          longitude = 77.229446;

          // Update userLocationData with valid coordinates
          userLocationData = LocationData.fromMap({
            'latitude': latitude,
            'longitude': longitude,
            'accuracy': 0.0,
            'altitude': 0.0,
            'speed': 0.0,
            'speed_accuracy': 0.0,
            'heading': 0.0,
            'time': DateTime.now().millisecondsSinceEpoch
          });
        }

        userLocationStream.updateUserlocationDataResponse(userLocationData!);

        // Get nearby vehicles with validated coordinates
        http.Response nearByVehiclesResponse = await BackendApi.initiateGetCall(
            ApiUrls.availableVehicles,
            params: {
              "latitude": latitude,
              "longitude": longitude,
              "range": selectedRange
            });

        debugPrint(
            'Available vehicles API response status: ${nearByVehiclesResponse.statusCode}');
        debugPrint(
            'Available vehicles API response body: ${nearByVehiclesResponse.body}');

        // Check if API call was successful
        if (nearByVehiclesResponse.statusCode != 200) {
          debugPrint(
              'Available vehicles API failed with status: ${nearByVehiclesResponse.statusCode}');
          // For nearby vehicles, don't show error for 404 (no vehicles found)
          if (nearByVehiclesResponse.statusCode == 404) {
            debugPrint('No nearby vehicles found - handled silently');
          } else {
            // Show error message to user for other failures
            debugPrint('Showing error for available vehicles API failure');
            ApiErrorHandler.handleHttpError(
                nearByVehiclesResponse.statusCode, 'availableVehicles');
          }

          // Set empty data to indicate no vehicles available
          nearbyPOI = NearbyPOI(nearByVehicles: []);
          currentNearbyPOI = nearbyPOI;
          nearByPOIStream.updateNearbyVehiclesResponse(nearbyPOI);

          // Wait before retrying
          await Future.delayed(const Duration(seconds: 10));
          continue;
        }

        debugPrint('Available vehicles API successful, parsing response...');
        // Parse response
        Map<String, dynamic> nearByVehiclesDecoded =
            decoder.convert(nearByVehiclesResponse.body);
        nearbyPOI = NearbyPOI.fromJson(nearByVehiclesDecoded);
        debugPrint(
            'Successfully parsed ${nearbyPOI.nearByVehicles?.length ?? 0} nearby vehicles');

        // Sort and reorganize nearby vehicles
        if (nearbyPOI.nearByVehicles != null) {
          nearbyPOI.nearByVehicles
              ?.sort((a, b) => b.charge!.compareTo(a.charge!));

          List<NearByVehicle> nearByVehicles = [];
          nearbyPOI.nearByVehicles?.forEach((element) {
            if (vehicleInfoConstant != null &&
                vehicleInfoConstant?.regNo == (element.regNo ?? "")) {
              nearByVehicles.insert(0, element);
            } else {
              nearByVehicles.add(element);
            }
          });
          nearbyPOI.nearByVehicles = nearByVehicles;
        } else {
          // Handle case when there are no nearby vehicles
          nearbyPOI.nearByVehicles = [];
        }

        currentNearbyPOI = nearbyPOI;

        // Update markers
        markers.clear();
        if (nearbyPOI.nearByVehicles != null &&
            nearbyPOI.nearByVehicles!.isNotEmpty) {
          await setMarkers(nearbyPOI.nearByVehicles!);
        }

        nearByPOIStream.updateNearbyVehiclesResponse(nearbyPOI);

        // Set map bounds if needed
        List<NearByVehicle> nearByVehicles = nearbyPOI.nearByVehicles ?? [];
        if (_point2.latitude == 0 &&
            _point2.longitude == 0 &&
            nearByVehicles.isNotEmpty) {
          // Use the first vehicle's location, ensuring it's not null or zero
          double vehicleLat = nearByVehicles.first.latitude ?? 0;
          double vehicleLng = nearByVehicles.first.longitude ?? 0;

          // If vehicle location is invalid, use user location
          if (vehicleLat == 0 || vehicleLng == 0) {
            vehicleLat = latitude;
            vehicleLng = longitude;
          }

          _point2 = LatLng(vehicleLat, vehicleLng);
          _setMapFitToBounds();
        }
      } catch (e) {
        // Show user-friendly error message
        debugPrint('Exception in available vehicles API call: $e');
        ApiErrorHandler.showApiError('availableVehicles');

        // Set empty data to indicate no vehicles available
        nearbyPOI = NearbyPOI(nearByVehicles: []);
        currentNearbyPOI = nearbyPOI;
        nearByPOIStream.updateNearbyVehiclesResponse(nearbyPOI);
      }

      // Wait before next refresh
      await Future.delayed(const Duration(seconds: 10));
    }
  }

  void openGoogleMaps({
    required double userLat,
    required double userLong,
    required double destLat,
    required double destLong,
  }) async {
    final googleMapsUrl =
        'https://www.google.com/maps/dir/?api=1&origin=$userLat,$userLong&destination=$destLat,$destLong&travelmode=driving';

    final Uri url = Uri.parse(googleMapsUrl);
    if (await canLaunchUrl(url)) {
      try {
        await launchUrl(url);
      } catch (e) {
        CustomToast.error('Failed to open Google Maps: ${e.toString()}');
      }
    } else {
      CustomToast.error(
          'Google Maps app is not installed or not supported on this device.');
    }
  }

  @override
  void dispose() {
    isNavigated = true;
    _selectedLeadershipVehicleController.close();
    _markersController.close();
    _zoomLogTimer?.cancel();
    super.dispose();
  }
}
