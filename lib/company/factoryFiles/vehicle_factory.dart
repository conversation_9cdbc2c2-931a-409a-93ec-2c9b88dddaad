import 'package:flutter/material.dart';
import 'package:nds_app/core/features/feature_service.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/company/baseScreens/vehicle/base_vehicle.dart';
import 'package:nds_app/company/templates/vehicle/vehicle_standard_screen.dart';
import 'package:nds_app/company/templates/vehicle/prodred_leadership_vehicles_screen.dart';

/// Factory class to create company-specific vehicle screens based on the current company configuration
class VehicleFactory {
  /// Creates and returns the appropriate vehicle widget based on the company name
  static Widget createVehicle({
    required Color color,
    required ColorType colorType,
  }) {
    // All companies use the same standard screen
    // Company-specific logic is handled by common widgets
    return _getVehicleTemplate(color: color, colorType: colorType);
  }

  /// Returns the appropriate vehicle template based on company validation requirements
  static BaseVehicle _getVehicleTemplate({
    required Color color,
    required ColorType colorType,
  }) {
    // Check if it's ProdRed leadership app
    if (FeatureService.hasLeadershipRole()) {
      return ProdredLeadershipVehiclesScreen(
          color: color, colorType: colorType);
    }

    // All companies use standard template
    return VehicleStandardScreen(color: color, colorType: colorType);
  }

  /// Returns the vehicle class name for debugging purposes
  static String getVehicleClassName() {
    if (FeatureService.hasLeadershipRole()) {
      return 'ProdredLeadershipVehiclesScreen';
    }
    return 'VehicleStandardScreen';
  }
}
